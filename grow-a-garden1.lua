local Rayfield = loadstring(game:HttpGet('https://sirius.menu/rayfield'))()

-- Add required services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local LocalPlayer = Players.LocalPlayer
local PlayerGui = LocalPlayer.PlayerGui
local Backpack = LocalPlayer.Backpack
local GameEvents = ReplicatedStorage.GameEvents
local Leaderstats = LocalPlayer.leaderstats
local ShecklesCount = Leaderstats.Sheckles

-- Farming related variables
local Farms = workspace.Farm
local OwnedSeeds = {}
local HarvestIgnores = {
    Normal = false,
    Gold = false,
    Rainbow = false
}

-- Farm automation variables
local SelectedFarmSeed = { Selected = "" }
local AutoPlantEnabled = false
local AutoSellEnabled = false
local SellThreshold = 15
local AutoPlantRandom = false
local farmStockCheckConnection = nil

-- Seed stock variables
local SeedStock = {}
local SelectedSeedStock = { Selected = {} } -- Changed to table for multiple seeds
local lastStockCheck = {}
local autoBuyEnabled = false
local stockCheckConnection = nil

-- Gear stock variables
local GearStock = {}
local SelectedGearStock = { Selected = {} } -- Changed to table for multiple gears
local lastGearStockCheck = {}
local gearAutoBuyEnabled = false
local gearStockCheckConnection = nil

-- Egg stock variables
local EggStock = {}
local SelectedEggStock = { Selected = {} } -- Changed to table for multiple eggs
local lastEggStockCheck = {}
local eggAutoBuyEnabled = false
local eggStockCheckConnection = nil

-- Function to get seed stock
local function GetSeedStock(IgnoreNoStock)
	local SeedShop = PlayerGui.Seed_Shop
	local Items = SeedShop:FindFirstChild("Blueberry", true).Parent

	local NewList = {}

	for _, Item in next, Items:GetChildren() do
		local MainFrame = Item:FindFirstChild("Main_Frame")
		if not MainFrame then continue end

		local StockText = MainFrame.Stock_Text.Text
		local StockCount = tonumber(StockText:match("%d+"))

		--// Seperate list
		if IgnoreNoStock then
			if StockCount <= 0 then continue end
			NewList[Item.Name] = StockCount
			continue
		end

		SeedStock[Item.Name] = StockCount
	end

	return IgnoreNoStock and NewList or SeedStock
end

-- Function to buy seed
local function BuySeed(Seed: string)
	GameEvents.BuySeedStock:FireServer(Seed)
end

-- Function to get gear stock
local function GetGearStock(IgnoreNoStock)
	local GearShop = PlayerGui.Gear_Shop -- Assuming gear shop is named Gear_Shop
	if not GearShop then
		print("Gear_Shop not found in PlayerGui")
		return {}
	end

	-- Try to find gear items - assuming similar structure to seed shop
	local Items = GearShop:FindFirstChild("WateringCan")
	if not Items then
		-- Try to find any frame that contains gear items
		for _, child in pairs(GearShop:GetDescendants()) do
			if child:IsA("Frame") and child:FindFirstChild("Main_Frame") then
				Items = child.Parent
				break
			end
		end
	end

	if not Items then
		print("Could not find gear items container")
		return {}
	end

	local NewList = {}

	for _, Item in next, Items:GetChildren() do
		local MainFrame = Item:FindFirstChild("Main_Frame")
		if not MainFrame then continue end

		local StockText = MainFrame:FindFirstChild("Stock_Text")
		if not StockText then continue end

		local StockCount = tonumber(StockText.Text:match("%d+")) or 0

		--// Separate list
		if IgnoreNoStock then
			if StockCount <= 0 then continue end
			NewList[Item.Name] = StockCount
			continue
		end

		GearStock[Item.Name] = StockCount
	end

	return IgnoreNoStock and NewList or GearStock
end

-- Function to buy gear
local function BuyGear(Gear: string)
	GameEvents.BuyGearStock:FireServer(Gear) -- Assuming similar event structure
end

-- Function to get egg stock
local function GetEggStock(IgnoreNoStock)
	-- Try multiple possible names for egg shop
	local EggShop = PlayerGui:FindFirstChild("PetShop_UI")
		or PlayerGui:FindFirstChild("Pet_Egg_Shop")
		or PlayerGui:FindFirstChild("Egg_Shop")
		or PlayerGui:FindFirstChild("PetEggShop")
		or PlayerGui:FindFirstChild("EggShop")

	if not EggShop then
		print("Egg_Shop not found in PlayerGui. Available GUIs:")
		for _, gui in pairs(PlayerGui:GetChildren()) do
			if gui:IsA("ScreenGui") then
				print("  - " .. gui.Name)
			end
		end
		return {}
	end

	print("Found EggShop:", EggShop.Name)

	-- Try to find egg items container - look for common patterns
	local Items = nil

	-- Method 1: Look for frames with Main_Frame children
	for _, child in pairs(EggShop:GetDescendants()) do
		if child:IsA("Frame") and child:FindFirstChild("Main_Frame") then
			Items = child.Parent
			print("Found Items container via Main_Frame:", Items.Name)
			break
		end
	end

	-- Method 2: Look for ScrollingFrame or Frame that contains multiple items
	if not Items then
		for _, child in pairs(EggShop:GetDescendants()) do
			if child:IsA("ScrollingFrame") or child:IsA("Frame") then
				local childCount = 0
				for _, subchild in pairs(child:GetChildren()) do
					if subchild:IsA("Frame") then
						childCount = childCount + 1
					end
				end
				if childCount > 1 then -- If it has multiple frame children, likely the items container
					Items = child
					print("Found Items container via multiple frames:", Items.Name)
					break
				end
			end
		end
	end

	if not Items then
		print("Could not find egg items container. EggShop structure:")
		for _, child in pairs(EggShop:GetDescendants()) do
			if child:IsA("Frame") or child:IsA("ScrollingFrame") then
				print("  - " .. child:GetFullName())
			end
		end
		return {}
	end

	local NewList = {}

	print("Scanning items in:", Items.Name)
	for _, Item in next, Items:GetChildren() do
		if not Item:IsA("Frame") then continue end

		print("Checking item:", Item.Name)

		local MainFrame = Item:FindFirstChild("Main_Frame")
		if not MainFrame then
			print("  No Main_Frame found")
			continue
		end

		local StockText = MainFrame:FindFirstChild("Stock_Text")
		if not StockText then
			print("  No Stock_Text found")
			continue
		end

		local StockCount = tonumber(StockText.Text:match("%d+")) or 0
		print("  Stock:", StockCount)

		--// Separate list
		if IgnoreNoStock then
			if StockCount <= 0 then continue end
			NewList[Item.Name] = StockCount
			continue
		end

		EggStock[Item.Name] = StockCount
	end

	print("Total eggs found:", #NewList > 0 and #NewList or "using EggStock table")
	return IgnoreNoStock and NewList or EggStock
end

-- Function to buy egg
local function BuyEgg(Egg: string)
	-- First, let's check what remote events are available
	print("=== Available Remote Events in GameEvents ===")
	for _, child in pairs(GameEvents:GetChildren()) do
		if child:IsA("RemoteEvent") then
			print("RemoteEvent found:", child.Name)
		end
	end
	print("=== End of Remote Events List ===")

	-- Try different possible names for egg buying
	local possibleNames = {
		"BuyEggStock",
		"BuyEgg",
		"PurchaseEgg",
		"BuyPetEgg",
		"PetEggPurchase",
		"EggPurchase",
		"BuyPet",
		"PurchasePet"
	}

	local foundEvent = nil
	for _, eventName in pairs(possibleNames) do
		local event = GameEvents:FindFirstChild(eventName)
		if event and event:IsA("RemoteEvent") then
			foundEvent = event
			print("Found egg buying event:", eventName)
			break
		end
	end

	if foundEvent then
		foundEvent:FireServer(Egg)
		print("Successfully fired event for egg:", Egg)
	else
		print("ERROR: No egg buying remote event found!")
		print("Please check the console output above for available events.")
	end
end

-- Function to buy all selected eggs
local function BuyAllSelectedEggs()
    local selectedEggs = SelectedEggStock.Selected

    print("BuyAllSelectedEggs called")
    print("Selected eggs type:", type(selectedEggs))

    -- Handle different types of selectedEggs
    if type(selectedEggs) == "table" then
        print("Selected eggs table:")
        for k, v in pairs(selectedEggs) do
            print("  ", k, "=", v)
        end
    else
        print("Selected eggs value:", selectedEggs)
    end

    -- Check if we have any selected eggs - handle both table and other formats
    local eggsToProcess = {}

    if type(selectedEggs) == "table" then
        -- If it's a table, check if it has values
        local hasEggs = false
        for k, v in pairs(selectedEggs) do
            if v == true or (type(v) == "string" and v ~= "") then
                table.insert(eggsToProcess, type(k) == "string" and k or v)
                hasEggs = true
            elseif type(k) == "number" and type(v) == "string" and v ~= "" then
                table.insert(eggsToProcess, v)
                hasEggs = true
            end
        end

        if not hasEggs then
            print("No eggs selected (empty table)")
            return
        end
    else
        print("Selected eggs is not a table")
        return
    end

    print("Eggs to process:", eggsToProcess)

    -- Update stock before buying
    GetEggStock()

    -- Iterate through all selected eggs
    for _, eggName in pairs(eggsToProcess) do
        local stock = EggStock[eggName]
        print("Checking egg:", eggName, "Stock:", stock)

        -- Only buy if egg is in stock
        if stock and stock > 0 then
            print("Buying", stock, "of", eggName)
            for i = 1, stock do
                BuyEgg(eggName)
            end
        else
            print("Egg", eggName, "not in stock or stock is 0")
        end
    end
end

-- Function to buy all selected gears
local function BuyAllSelectedGears()
    local selectedGears = SelectedGearStock.Selected

    print("BuyAllSelectedGears called")
    print("Selected gears type:", type(selectedGears))

    -- Handle different types of selectedGears
    if type(selectedGears) == "table" then
        print("Selected gears table:")
        for k, v in pairs(selectedGears) do
            print("  ", k, "=", v)
        end
    else
        print("Selected gears value:", selectedGears)
    end

    -- Check if we have any selected gears - handle both table and other formats
    local gearsToProcess = {}

    if type(selectedGears) == "table" then
        -- If it's a table, check if it has values
        local hasGears = false
        for k, v in pairs(selectedGears) do
            if v == true or (type(v) == "string" and v ~= "") then
                table.insert(gearsToProcess, type(k) == "string" and k or v)
                hasGears = true
            elseif type(k) == "number" and type(v) == "string" and v ~= "" then
                table.insert(gearsToProcess, v)
                hasGears = true
            end
        end

        if not hasGears then
            print("No gears selected (empty table)")
            return
        end
    else
        print("Selected gears is not a table")
        return
    end

    print("Gears to process:", gearsToProcess)

    -- Update stock before buying
    GetGearStock()

    -- Iterate through all selected gears
    for _, gearName in pairs(gearsToProcess) do
        local stock = GearStock[gearName]
        print("Checking gear:", gearName, "Stock:", stock)

        -- Only buy if gear is in stock
        if stock and stock > 0 then
            print("Buying", stock, "of", gearName)
            for i = 1, stock do
                BuyGear(gearName)
            end
        else
            print("Gear", gearName, "not in stock or stock is 0")
        end
    end
end

-- Function to buy all selected seeds
local function BuyAllSelectedSeeds()
    local selectedSeeds = SelectedSeedStock.Selected

    print("BuyAllSelectedSeeds called")
    print("Selected seeds type:", type(selectedSeeds))

    -- Handle different types of selectedSeeds
    if type(selectedSeeds) == "table" then
        print("Selected seeds table:")
        for k, v in pairs(selectedSeeds) do
            print("  ", k, "=", v)
        end
    else
        print("Selected seeds value:", selectedSeeds)
    end

    -- Check if we have any selected seeds - handle both table and other formats
    local seedsToProcess = {}

    if type(selectedSeeds) == "table" then
        -- If it's a table, check if it has values
        local hasSeeds = false
        for k, v in pairs(selectedSeeds) do
            if v == true or (type(v) == "string" and v ~= "") then
                table.insert(seedsToProcess, type(k) == "string" and k or v)
                hasSeeds = true
            elseif type(k) == "number" and type(v) == "string" and v ~= "" then
                table.insert(seedsToProcess, v)
                hasSeeds = true
            end
        end

        if not hasSeeds then
            print("No seeds selected (empty table)")
            return
        end
    else
        print("Selected seeds is not a table")
        return
    end

    print("Seeds to process:", seedsToProcess)

    -- Update stock before buying
    GetSeedStock()

    -- Iterate through all selected seeds
    for _, seedName in pairs(seedsToProcess) do
        local stock = SeedStock[seedName]
        print("Checking seed:", seedName, "Stock:", stock)

        -- Only buy if seed is in stock
        if stock and stock > 0 then
            print("Buying", stock, "of", seedName)
            for i = 1, stock do
                BuySeed(seedName)
            end
        else
            print("Seed", seedName, "not in stock or stock is 0")
        end
    end
end

-- Function to check stock changes and auto-buy
local function startStockChecking()
    stockCheckConnection = game:GetService("RunService").Heartbeat:Connect(function()
        if not autoBuyEnabled or not SelectedSeedStock.Selected then return end

        -- Check if we have selected seeds
        local hasSelectedSeeds = false
        if type(SelectedSeedStock.Selected) == "table" then
            for k, v in pairs(SelectedSeedStock.Selected) do
                if v == true or (type(v) == "string" and v ~= "") then
                    hasSelectedSeeds = true
                    break
                end
            end
        end

        if not hasSelectedSeeds then return end

        local currentStock = GetSeedStock()
        local selectedSeeds = SelectedSeedStock.Selected
        local shouldBuy = false

        -- Process selected seeds based on their format
        local seedsToCheck = {}
        if type(selectedSeeds) == "table" then
            for k, v in pairs(selectedSeeds) do
                if v == true or (type(v) == "string" and v ~= "") then
                    local seedName = type(k) == "string" and k or v
                    if type(seedName) == "string" and seedName ~= "" then
                        table.insert(seedsToCheck, seedName)
                    end
                elseif type(k) == "number" and type(v) == "string" and v ~= "" then
                    table.insert(seedsToCheck, v)
                end
            end
        end

        -- Check if stock has changed for any selected seed OR if we haven't checked before
        for _, seedName in pairs(seedsToCheck) do
            local previousStock = lastStockCheck[seedName] or -1 -- Use -1 if never checked before

            if previousStock ~= currentStock[seedName] then
                lastStockCheck[seedName] = currentStock[seedName]
                print("Stock changed for " .. seedName .. ": " .. (currentStock[seedName] or 0))

                -- If any selected seed is available, trigger purchase
                if currentStock[seedName] and currentStock[seedName] > 0 then
                    shouldBuy = true
                    print("Will buy " .. seedName .. " - Stock: " .. currentStock[seedName])
                end
            end
        end

        -- Buy all available selected seeds if any stock changed
        if shouldBuy then
            print("Triggering purchase for available seeds")
            BuyAllSelectedSeeds()
        end
    end)
end

-- Function to stop stock checking
local function stopStockChecking()
    if stockCheckConnection then
        stockCheckConnection:Disconnect()
        stockCheckConnection = nil
    end
end

-- Function to check gear stock changes and auto-buy
local function startGearStockChecking()
    gearStockCheckConnection = game:GetService("RunService").Heartbeat:Connect(function()
        if not gearAutoBuyEnabled or not SelectedGearStock.Selected then return end

        -- Check if we have selected gears
        local hasSelectedGears = false
        if type(SelectedGearStock.Selected) == "table" then
            for k, v in pairs(SelectedGearStock.Selected) do
                if v == true or (type(v) == "string" and v ~= "") then
                    hasSelectedGears = true
                    break
                end
            end
        end

        if not hasSelectedGears then return end

        local currentStock = GetGearStock()
        local selectedGears = SelectedGearStock.Selected
        local shouldBuy = false

        -- Process selected gears based on their format
        local gearsToCheck = {}
        if type(selectedGears) == "table" then
            for k, v in pairs(selectedGears) do
                if v == true or (type(v) == "string" and v ~= "") then
                    local gearName = type(k) == "string" and k or v
                    if type(gearName) == "string" and gearName ~= "" then
                        table.insert(gearsToCheck, gearName)
                    end
                elseif type(k) == "number" and type(v) == "string" and v ~= "" then
                    table.insert(gearsToCheck, v)
                end
            end
        end

        -- Check if stock has changed for any selected gear OR if we haven't checked before
        for _, gearName in pairs(gearsToCheck) do
            local previousStock = lastGearStockCheck[gearName] or -1 -- Use -1 if never checked before

            if previousStock ~= currentStock[gearName] then
                lastGearStockCheck[gearName] = currentStock[gearName]
                print("Gear stock changed for " .. gearName .. ": " .. (currentStock[gearName] or 0))

                -- If any selected gear is available, trigger purchase
                if currentStock[gearName] and currentStock[gearName] > 0 then
                    shouldBuy = true
                    print("Will buy " .. gearName .. " - Stock: " .. currentStock[gearName])
                end
            end
        end

        -- Buy all available selected gears if any stock changed
        if shouldBuy then
            print("Triggering purchase for available gears")
            BuyAllSelectedGears()
        end
    end)
end

-- Function to stop gear stock checking
local function stopGearStockChecking()
    if gearStockCheckConnection then
        gearStockCheckConnection:Disconnect()
        gearStockCheckConnection = nil
    end
end

-- Function to check egg stock changes and auto-buy
local function startEggStockChecking()
    eggStockCheckConnection = game:GetService("RunService").Heartbeat:Connect(function()
        if not eggAutoBuyEnabled or not SelectedEggStock.Selected then return end

        -- Check if we have selected eggs
        local hasSelectedEggs = false
        if type(SelectedEggStock.Selected) == "table" then
            for k, v in pairs(SelectedEggStock.Selected) do
                if v == true or (type(v) == "string" and v ~= "") then
                    hasSelectedEggs = true
                    break
                end
            end
        end

        if not hasSelectedEggs then return end

        local currentStock = GetEggStock()
        local selectedEggs = SelectedEggStock.Selected
        local shouldBuy = false

        -- Process selected eggs based on their format
        local eggsToCheck = {}
        if type(selectedEggs) == "table" then
            for k, v in pairs(selectedEggs) do
                if v == true or (type(v) == "string" and v ~= "") then
                    local eggName = type(k) == "string" and k or v
                    if type(eggName) == "string" and eggName ~= "" then
                        table.insert(eggsToCheck, eggName)
                    end
                elseif type(k) == "number" and type(v) == "string" and v ~= "" then
                    table.insert(eggsToCheck, v)
                end
            end
        end

        -- Check if stock has changed for any selected egg OR if we haven't checked before
        for _, eggName in pairs(eggsToCheck) do
            local previousStock = lastEggStockCheck[eggName] or -1 -- Use -1 if never checked before

            if previousStock ~= currentStock[eggName] then
                lastEggStockCheck[eggName] = currentStock[eggName]
                print("Egg stock changed for " .. eggName .. ": " .. (currentStock[eggName] or 0))

                -- If any selected egg is available, trigger purchase
                if currentStock[eggName] and currentStock[eggName] > 0 then
                    shouldBuy = true
                    print("Will buy " .. eggName .. " - Stock: " .. currentStock[eggName])
                end
            end
        end

        -- Buy all available selected eggs if any stock changed
        if shouldBuy then
            print("Triggering purchase for available eggs")
            BuyAllSelectedEggs()
        end
    end)
end

-- Function to stop egg stock checking
local function stopEggStockChecking()
    if eggStockCheckConnection then
        eggStockCheckConnection:Disconnect()
        eggStockCheckConnection = nil
    end
end

-- Farming functions
local function Plant(Position, Seed)
    GameEvents.Plant_RE:FireServer(Position, Seed)
    wait(.3)
end

local function GetFarms()
    return Farms:GetChildren()
end

local function GetFarmOwner(Farm)
    local Important = Farm.Important
    local Data = Important.Data
    local Owner = Data.Owner
    return Owner.Value
end

local function GetFarm(PlayerName)
    local Farms = GetFarms()
    for _, Farm in next, Farms do
        local Owner = GetFarmOwner(Farm)
        if Owner == PlayerName then
            return Farm
        end
    end
    return nil
end

local IsSelling = false
local function SellInventory()
    local Character = LocalPlayer.Character
    local Previous = Character:GetPivot()
    local PreviousSheckles = ShecklesCount.Value

    -- Prevent conflict
    if IsSelling then return end
    IsSelling = true

    Character:PivotTo(CFrame.new(62, 4, -26))
    while wait() do
        if ShecklesCount.Value ~= PreviousSheckles then break end
        GameEvents.Sell_Inventory:FireServer()
    end
    Character:PivotTo(Previous)

    wait(0.2)
    IsSelling = false
end

local function GetSeedInfo(Seed)
    local PlantName = Seed:FindFirstChild("Plant_Name")
    local Count = Seed:FindFirstChild("Numbers")
    if not PlantName then return end
    return PlantName.Value, Count.Value
end

local function CollectSeedsFromParent(Parent, Seeds)
    for _, Tool in next, Parent:GetChildren() do
        local Name, Count = GetSeedInfo(Tool)
        if not Name then continue end
        Seeds[Name] = {
            Count = Count,
            Tool = Tool
        }
    end
end

local function CollectCropsFromParent(Parent, Crops)
    for _, Tool in next, Parent:GetChildren() do
        local Name = Tool:FindFirstChild("Item_String")
        if not Name then continue end
        table.insert(Crops, Tool)
    end
end

local function GetOwnedSeeds()
    local Character = LocalPlayer.Character
    OwnedSeeds = {} -- Clear previous data
    CollectSeedsFromParent(Backpack, OwnedSeeds)
    CollectSeedsFromParent(Character, OwnedSeeds)
    return OwnedSeeds
end

local function GetInvCrops()
    local Character = LocalPlayer.Character
    local Crops = {}
    CollectCropsFromParent(Backpack, Crops)
    CollectCropsFromParent(Character, Crops)
    return Crops
end

local function GetArea(Base)
    local Center = Base:GetPivot()
    local Size = Base.Size

    -- Bottom left
    local X1 = math.ceil(Center.X - (Size.X/2))
    local Z1 = math.ceil(Center.Z - (Size.Z/2))

    -- Top right
    local X2 = math.floor(Center.X + (Size.X/2))
    local Z2 = math.floor(Center.Z + (Size.Z/2))

    return X1, Z1, X2, Z2
end

local function EquipCheck(Tool)
    local Character = LocalPlayer.Character
    local Humanoid = Character.Humanoid

    if Tool.Parent ~= Backpack then return end
    Humanoid:EquipTool(Tool)
end

-- Auto farm functions
local MyFarm = nil
local MyImportant = nil
local PlantLocations = nil
local PlantsPhysical = nil
local Dirt = nil
local X1, Z1, X2, Z2 = 0, 0, 0, 0

local function InitializeFarm()
    MyFarm = GetFarm(LocalPlayer.Name)
    if not MyFarm then return false end

    MyImportant = MyFarm.Important
    PlantLocations = MyImportant.Plant_Locations
    PlantsPhysical = MyImportant.Plants_Physical

    Dirt = PlantLocations:FindFirstChildOfClass("Part")
    if Dirt then
        X1, Z1, X2, Z2 = GetArea(Dirt)
    end

    return true
end

local function GetRandomFarmPoint()
    if not PlantLocations then return Vector3.new(0, 4, 0) end

    local FarmLands = PlantLocations:GetChildren()
    if #FarmLands == 0 then return Vector3.new(0, 4, 0) end

    local FarmLand = FarmLands[math.random(1, #FarmLands)]
    local X1, Z1, X2, Z2 = GetArea(FarmLand)
    local X = math.random(X1, X2)
    local Z = math.random(Z1, Z2)

    return Vector3.new(X, 4, Z)
end

local function AutoPlantLoop()
    print("=== AutoPlantLoop Starting ===")

    if not InitializeFarm() then
        print("ERROR: Could not initialize farm - Make sure you own a farm plot!")
        return
    end
    print("Farm initialized successfully")

    local Seed = SelectedFarmSeed.Selected
    print("Selected seed:", Seed, "Type:", type(Seed))

    -- Handle if Seed is a table (from dropdown)
    if type(Seed) == "table" then
        -- Get the first selected seed from table
        for seedName, selected in pairs(Seed) do
            if selected == true then
                Seed = seedName
                break
            end
        end
    end

    print("Final seed to use:", Seed)
    if not Seed or Seed == "" or type(Seed) ~= "string" then
        print("ERROR: No valid seed selected for farming")
        return
    end

    local SeedData = OwnedSeeds[Seed]
    print("Seed data:", SeedData)
    if not SeedData then
        print("ERROR: Seed not found in inventory:", Seed)
        print("Available seeds in inventory:")
        for seedName, data in pairs(OwnedSeeds) do
            print("  -", seedName, "Count:", data.Count)
        end
        return
    end

    local Count = SeedData.Count
    local Tool = SeedData.Tool
    print("Seed count:", Count, "Tool:", Tool)

    -- Check for stock
    if Count <= 0 then
        print("ERROR: No seeds in inventory, count is:", Count)
        return
    end

    local Planted = 0
    local Step = 1

    -- Check if the client needs to equip the tool
    print("Equipping tool...")
    EquipCheck(Tool)

    -- Plant at random points
    if AutoPlantRandom then
        print("Planting at random points...")
        for i = 1, Count do
            local Point = GetRandomFarmPoint()
            print("Planting at random point:", Point)
            Plant(Point, Seed)
            Planted = Planted + 1
        end
        print("Planted", Planted, "seeds at random points")
        return
    end

    -- Plant on the farmland area
    print("Planting on farmland area. Bounds:", X1, Z1, "to", X2, Z2)
    for X = X1, X2, Step do
        for Z = Z1, Z2, Step do
            if Planted >= Count then break end
            local Point = Vector3.new(X, 0.13, Z)
            print("Planting at:", Point)
            Plant(Point, Seed)
            Planted = Planted + 1
        end
        if Planted >= Count then break end
    end
    print("Total planted:", Planted, "seeds")
end

local function HarvestPlant(Plant)
    local Prompt = Plant:FindFirstChild("ProximityPrompt", true)
    -- Check if it can be harvested
    if not Prompt then return end
    fireproximityprompt(Prompt)
end

local function CanHarvest(Plant)
    local Prompt = Plant:FindFirstChild("ProximityPrompt", true)
    if not Prompt then return false end
    if not Prompt.Enabled then return false end
    return true
end

local function CollectHarvestable(Parent, Plants, IgnoreDistance)
    local Character = LocalPlayer.Character
    if not Character then return Plants end

    local PlayerPosition = Character:GetPivot().Position

    for _, Plant in next, Parent:GetChildren() do
        -- Fruits
        local Fruits = Plant:FindFirstChild("Fruits")
        if Fruits then
            CollectHarvestable(Fruits, Plants, IgnoreDistance)
        end

        -- Distance check
        local PlantPosition = Plant:GetPivot().Position
        local Distance = (PlayerPosition-PlantPosition).Magnitude
        if not IgnoreDistance and Distance > 15 then continue end

        -- Ignore check
        local Variant = Plant:FindFirstChild("Variant")
        if Variant and HarvestIgnores[Variant.Value] then continue end

        -- Collect
        if CanHarvest(Plant) then
            table.insert(Plants, Plant)
        end
    end
    return Plants
end

local function GetHarvestablePlants(IgnoreDistance)
    if not PlantsPhysical then return {} end

    local Plants = {}
    CollectHarvestable(PlantsPhysical, Plants, IgnoreDistance)
    return Plants
end

local function HarvestPlants()
    print("=== HarvestPlants Starting ===")
    local Plants = GetHarvestablePlants()
    print("Found", #Plants, "harvestable plants")

    for i, Plant in next, Plants do
        print("Harvesting plant", i, ":", Plant.Name)
        HarvestPlant(Plant)
    end
    print("Harvest completed")
end

local function AutoSellCheck()
    local CropCount = #GetInvCrops()
    print("=== AutoSellCheck ===")
    print("Crop count:", CropCount, "Threshold:", SellThreshold, "Auto sell enabled:", AutoSellEnabled)

    if not AutoSellEnabled then
        print("Auto sell is disabled")
        return
    end
    if CropCount < SellThreshold then
        print("Not enough crops to sell")
        return
    end

    print("Selling inventory...")
    SellInventory()
end

local function MakeLoop(Toggle, Func)
    coroutine.wrap(function()
        while wait(.01) do
            if not Toggle then continue end
            Func()
        end
    end)()
end

local function StartFarmServices()
    -- Auto-Harvest and Auto-Plant combined loop
    spawn(function()
        while true do
            wait(0.1)
            if AutoPlantEnabled then
                print("=== Farm Cycle Starting ===")
                GetOwnedSeeds() -- Update owned seeds
                print("Owned seeds updated")

                -- Show owned seeds for debugging
                for seedName, seedData in pairs(OwnedSeeds) do
                    print("Owned seed:", seedName, "Count:", seedData.Count)
                end

                HarvestPlants() -- Harvest first
                print("Harvest completed")

                AutoPlantLoop() -- Then plant
                print("Plant cycle completed")

                wait(2) -- Wait between cycles
            end
        end
    end)

    -- Auto-Sell check
    RunService.Heartbeat:Connect(function()
        if AutoSellEnabled then
            AutoSellCheck()
        end
    end)

    Backpack.ChildAdded:Connect(function()
        if AutoSellEnabled then
            AutoSellCheck()
        end
    end)
end

local Window = Rayfield:CreateWindow({
    Name = "หมา " .. 1.0,
    LoadingTitle = "by XZery",
    LoadingSubtitle = "Loading...",
    ConfigurationSaving = {
        Enabled = true,
        FolderName = "RayfieldScriptHub",
        FileName = "grow-a-garden"
    },
    Discord = {
        Enabled = false,
        Invite = "noinvitelink",
        RememberJoins = true
    },
    KeySystem = false,
    KeySettings = {
        Title = "Untitled",
        Subtitle = "Key System",
        Note = "No method of obtaining the key is provided",
        FileName = "Key",
        SaveKey = true,
        GrabKeyFromSite = false,
        Key = {"Hello"}
    }
})

local Tabs = {
    Main = Window:CreateTab("Main", 4483362458),
    Seed = Window:CreateTab("Seed", 4483362458),
    Gear = Window:CreateTab("Gear", 4483362458),
    Egg = Window:CreateTab("Egg", 4483362458),
    Farm = Window:CreateTab("Farm", 4483362458)
}

local speed = Tabs.Main:CreateSlider({
    Name = "Speed",
    Range = {16, 200},
    Increment = 1,
    Suffix = "",
    CurrentValue = 16,
    Flag = "SpeedSlider",
    Callback = function(Value)
        local player = game.Players.LocalPlayer
        local char = player.Character
        if char and char:FindFirstChild("Humanoid") then
            char.Humanoid.WalkSpeed = Value
        end
    end,
})

local jump = Tabs.Main:CreateSlider({
    Name = "Jump Power",
    Range = {50, 200},
    Increment = 1,
    Suffix = "",
    CurrentValue = 50,
    Flag = "JumpSlider",
    Callback = function(Value)
        local player = game.Players.LocalPlayer
        local char = player.Character
        if char and char:FindFirstChild("Humanoid") then
            char.Humanoid.JumpPower = Value
        end
    end,
})

    -- Fly toggle functionality
    local flyEnabled = false
    local bodyVelocity = nil
    local bodyAngularVelocity = nil
    local connection = nil

    local function enableFly()
        local player = game.Players.LocalPlayer
        local char = player.Character
        if not char or not char:FindFirstChild("HumanoidRootPart") then return end

        local humanoidRootPart = char.HumanoidRootPart

        -- Create BodyVelocity for movement
        bodyVelocity = Instance.new("BodyVelocity")
        bodyVelocity.MaxForce = Vector3.new(9e9, 9e9, 9e9)
        bodyVelocity.Velocity = Vector3.new(0, 0, 0)
        bodyVelocity.Parent = humanoidRootPart

        -- Create BodyAngularVelocity for rotation control
        bodyAngularVelocity = Instance.new("BodyAngularVelocity")
        bodyAngularVelocity.MaxTorque = Vector3.new(0, 9e9, 0)
        bodyAngularVelocity.AngularVelocity = Vector3.new(0, 0, 0)
        bodyAngularVelocity.Parent = humanoidRootPart

        -- Movement control
        connection = game:GetService("RunService").Heartbeat:Connect(function()
            local camera = workspace.CurrentCamera
            local userInputService = game:GetService("UserInputService")

            local velocity = Vector3.new(0, 0, 0)
            local flySpeed = 50

            -- Get camera directions
            local lookDirection = camera.CFrame.LookVector
            local rightDirection = camera.CFrame.RightVector
            local upDirection = Vector3.new(0, 1, 0)

            -- Simple directional movement based on camera
            if userInputService:IsKeyDown(Enum.KeyCode.W) then
                velocity = velocity + lookDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.S) then
                velocity = velocity - lookDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.A) then
                velocity = velocity - rightDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.D) then
                velocity = velocity + rightDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.Space) then
                velocity = velocity + upDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.LeftShift) then
                velocity = velocity - upDirection * flySpeed
            end

            bodyVelocity.Velocity = velocity
        end)
    end

    local function disableFly()
        if bodyVelocity then
            bodyVelocity:Destroy()
            bodyVelocity = nil
        end
        if bodyAngularVelocity then
            bodyAngularVelocity:Destroy()
            bodyAngularVelocity = nil
        end
        if connection then
            connection:Disconnect()
            connection = nil
        end
    end

    local flyToggle = Tabs.Main:CreateToggle({
        Name = "Fly",
        CurrentValue = false,
        Flag = "FlyToggle",
        Callback = function(Value)
            flyEnabled = Value
            if flyEnabled then
                enableFly()
            else
                disableFly()
            end
        end,
    })

    -- Reset Character Button
    local resetButton = Tabs.Main:CreateButton({
        Name = "Reset Character",
        Callback = function()
            local player = game.Players.LocalPlayer
            if player.Character then
                player.Character:BreakJoints()
            end
        end,
    })

    -- Seed stock dropdown (multi-select) - moved to Seed tab
    local seedDropdown = Tabs.Seed:CreateDropdown({
        Name = "Select Seeds",
        Options = {},
        CurrentOption = {},
        MultipleOptions = true,
        Flag = "SeedDropdown",
        Callback = function(Value)
            print("Dropdown OnChanged called")
            print("Value type:", type(Value))

            if type(Value) == "table" then
                print("Value contents:")
                for k, v in pairs(Value) do
                    print("  ", k, "=", v, "(", type(k), "->", type(v), ")")
                end
            else
                print("Value:", Value)
            end

            SelectedSeedStock.Selected = Value -- Value is now a table of selected seeds
            -- Update stock data when selection changes
            GetSeedStock()
        end,
    })

    -- Function to update seed dropdown
    local function updateSeedDropdown()
        local allSeeds = GetSeedStock(false) -- Get all seeds regardless of stock
        local seedList = {}

        for seedName, _ in pairs(allSeeds) do
            table.insert(seedList, seedName)
        end

        seedDropdown:Refresh(seedList, true)
    end

    -- Auto-buy toggle - moved to Seed tab
    local autoBuyToggle = Tabs.Seed:CreateToggle({
        Name = "Auto-Buy Seeds",
        CurrentValue = false,
        Flag = "AutoBuyToggle",
        Callback = function(Value)
            autoBuyEnabled = Value
            if autoBuyEnabled then
                startStockChecking()
            else
                stopStockChecking()
            end
        end,
    })

    -- Note: Callback is now handled in the dropdown creation above



    -- Initial seed dropdown update
    updateSeedDropdown()

    -- Load saved seed selections (will be loaded after SaveManager is properly initialized)

    -- Gear stock dropdown (multi-select)
    local gearDropdown = Tabs.Gear:CreateDropdown({
        Name = "Select Gears",
        Options = {},
        CurrentOption = {},
        MultipleOptions = true,
        Flag = "GearDropdown",
        Callback = function(Value)
            print("Gear Dropdown OnChanged called")
            print("Value type:", type(Value))

            if type(Value) == "table" then
                print("Value contents:")
                for k, v in pairs(Value) do
                    print("  ", k, "=", v, "(", type(k), "->", type(v), ")")
                end
            else
                print("Value:", Value)
            end

            SelectedGearStock.Selected = Value -- Value is now a table of selected gears
            -- Update stock data when selection changes
            GetGearStock()
        end,
    })

    -- Function to update gear dropdown
    local function updateGearDropdown()
        local allGears = GetGearStock(false) -- Get all gears regardless of stock
        local gearList = {}

        for gearName, _ in pairs(allGears) do
            table.insert(gearList, gearName)
        end

        gearDropdown:Refresh(gearList, true)
    end

    -- Gear auto-buy toggle
    local gearAutoBuyToggle = Tabs.Gear:CreateToggle({
        Name = "Auto-Buy Gears",
        CurrentValue = false,
        Flag = "GearAutoBuyToggle",
        Callback = function(Value)
            gearAutoBuyEnabled = Value
            if gearAutoBuyEnabled then
                startGearStockChecking()
            else
                stopGearStockChecking()
            end
        end,
    })

    -- Note: Callback is now handled in the dropdown creation above

    -- Initial gear dropdown update
    updateGearDropdown()

    -- Egg stock dropdown (multi-select)
    local eggDropdown = Tabs.Egg:CreateDropdown({
        Name = "Select Eggs",
        Options = {},
        CurrentOption = {},
        MultipleOptions = true,
        Flag = "EggDropdown",
        Callback = function(Value)
            print("Egg Dropdown OnChanged called")
            print("Value type:", type(Value))

            if type(Value) == "table" then
                print("Value contents:")
                for k, v in pairs(Value) do
                    print("  ", k, "=", v, "(", type(k), "->", type(v), ")")
                end
            else
                print("Value:", Value)
            end

            SelectedEggStock.Selected = Value -- Value is now a table of selected eggs
            -- Update stock data when selection changes
            GetEggStock()
        end,
    })

    -- Function to update egg dropdown
    local function updateEggDropdown()
        local allEggs = GetEggStock(false) -- Get all eggs regardless of stock
        local eggList = {}

        for eggName, _ in pairs(allEggs) do
            table.insert(eggList, eggName)
        end

        eggDropdown:Refresh(eggList, true)
    end

    -- Egg auto-buy toggle
    local eggAutoBuyToggle = Tabs.Egg:CreateToggle({
        Name = "Auto-Buy Eggs",
        CurrentValue = false,
        Flag = "EggAutoBuyToggle",
        Callback = function(Value)
            eggAutoBuyEnabled = Value
            if eggAutoBuyEnabled then
                startEggStockChecking()
            else
                stopEggStockChecking()
            end
        end,
    })

    -- Initial egg dropdown update
    updateEggDropdown()

    -- Farm Tab
    -- Function to get all available seeds for farming (not just owned)
    local function getAllFarmSeeds()
        local allSeeds = GetSeedStock(false) -- Get all seeds regardless of stock
        local seedList = {}

        for seedName, _ in pairs(allSeeds) do
            table.insert(seedList, seedName)
        end

        return seedList
    end

    -- Farm seed selection dropdown
    local farmSeedDropdown = Tabs.Farm:CreateDropdown({
        Name = "Select Seed for Farming",
        Options = getAllFarmSeeds(),
        CurrentOption = "",
        MultipleOptions = false,
        Flag = "FarmSeedDropdown",
        Callback = function(Value)
            SelectedFarmSeed.Selected = Value
            print("Selected farm seed:", Value)
        end,
    })

    -- Auto Plant toggle (includes harvesting)
    local autoPlantToggle = Tabs.Farm:CreateToggle({
        Name = "Auto Farm (Plant + Harvest)",
        CurrentValue = false,
        Flag = "AutoPlantToggle",
        Callback = function(Value)
            AutoPlantEnabled = Value
            if AutoPlantEnabled then
                print("Auto farming enabled")
                -- Initialize farm when starting
                if not InitializeFarm() then
                    print("Warning: Could not initialize farm. Make sure you own a farm plot.")
                end
            else
                print("Auto farming disabled")
            end
        end,
    })

    -- Plant at random points toggle
    local randomPlantToggle = Tabs.Farm:CreateToggle({
        Name = "Plant at Random Points",
        CurrentValue = false,
        Flag = "RandomPlantToggle",
        Callback = function(Value)
            AutoPlantRandom = Value
            print("Plant at random points:", Value)
        end,
    })

    -- Auto Sell toggle
    local autoSellToggle = Tabs.Farm:CreateToggle({
        Name = "Auto Sell",
        CurrentValue = false,
        Flag = "AutoSellToggle",
        Callback = function(Value)
            AutoSellEnabled = Value
            print("Auto sell enabled:", Value)
        end,
    })

    -- Sell threshold slider
    local sellThresholdSlider = Tabs.Farm:CreateSlider({
        Name = "Sell Threshold (Crops)",
        Range = {1, 199},
        Increment = 1,
        Suffix = " crops",
        CurrentValue = 15,
        Flag = "SellThresholdSlider",
        Callback = function(Value)
            SellThreshold = Value
            print("Sell threshold set to:", Value)
        end,
    })



    -- Harvest ignores toggles
    local ignoreNormalToggle = Tabs.Farm:CreateToggle({
        Name = "Ignore Normal Plants",
        CurrentValue = false,
        Flag = "IgnoreNormalToggle",
        Callback = function(Value)
            HarvestIgnores.Normal = Value
            print("Ignore normal plants:", Value)
        end,
    })

    local ignoreGoldToggle = Tabs.Farm:CreateToggle({
        Name = "Ignore Gold Plants",
        CurrentValue = false,
        Flag = "IgnoreGoldToggle",
        Callback = function(Value)
            HarvestIgnores.Gold = Value
            print("Ignore gold plants:", Value)
        end,
    })

    local ignoreRainbowToggle = Tabs.Farm:CreateToggle({
        Name = "Ignore Rainbow Plants",
        CurrentValue = false,
        Flag = "IgnoreRainbowToggle",
        Callback = function(Value)
            HarvestIgnores.Rainbow = Value
            print("Ignore rainbow plants:", Value)
        end,
    })

    -- Start farm services
    StartFarmServices()

-- Rayfield has built-in configuration saving, so we don't need separate SaveManager/InterfaceManager

Rayfield:Notify({
    Title = "Script Loaded",
    Content = "The script has been loaded successfully.",
    Duration = 6.5,
    Image = 4483362458,
    Actions = {
        Ignore = {
            Name = "Okay!",
            Callback = function()
                print("The user tapped Okay!")
            end
        },
    },
})